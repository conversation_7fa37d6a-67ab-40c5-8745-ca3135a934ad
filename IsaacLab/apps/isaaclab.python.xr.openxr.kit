##
# Adapted from: _isaac_sim/apps/isaacsim.exp.xr.openxr.kit
##

[package]
title = "Isaac Lab Python OpenXR"
description = "An app for running Isaac Lab with OpenXR"
version = "2.1.0"

# That makes it browsable in UI with "experience" filter
keywords = ["experience", "app", "usd"]

[settings]
# Note: This path was adapted to be respective to the kit-exe file location
app.versionFile = "${exe-path}/VERSION"
app.folder = "${exe-path}/"
app.name = "Isaac-Sim"
app.version = "4.5.0"

### async rendering settings
omni.replicator.asyncRendering = true
app.asyncRendering = true
app.asyncRenderingLowLatency = true

# For XR, set this back to default "#define OMNI_MAX_DEVICE_GROUP_DEVICE_COUNT 16"
renderer.multiGpu.maxGpuCount = 16
renderer.gpuEnumeration.glInterop.enabled = true # Allow Kit XR OpenXR to render headless

[dependencies]
"isaaclab.python" = {}
"isaacsim.xr.openxr" = {}

# Kit extensions
"omni.kit.xr.system.openxr" = {}
"omni.kit.xr.profile.ar" = {}

[settings]
app.xr.enabled = true

# xr settings
xr.ui.enabled = false
xr.depth.aov = "GBufferDepth"
defaults.xr.profile.ar.renderQuality = "off"
defaults.xr.profile.ar.anchorMode = "custom anchor"
rtx.rendermode = "RaytracedLighting"
persistent.xr.profile.ar.render.nearPlane = 0.15

# Register extension folder from this repo in kit
[settings.app.exts]
folders = [
    "${exe-path}/exts",  # kit extensions
    "${exe-path}/extscore",  # kit core extensions
    "${exe-path}/../exts",  # isaac extensions
    "${exe-path}/../extsDeprecated",  # deprecated isaac extensions
    "${exe-path}/../extscache",  # isaac cache extensions
    "${exe-path}/../extsPhysics",  # isaac physics extensions
    "${exe-path}/../isaacsim/exts",  # isaac extensions for pip
    "${exe-path}/../isaacsim/extsDeprecated",  # deprecated isaac extensions
    "${exe-path}/../isaacsim/extscache",  # isaac cache extensions for pip
    "${exe-path}/../isaacsim/extsPhysics",  # isaac physics extensions for pip
    "${app}", # needed to find other app files
    "${app}/../source", # needed to find extensions in Isaac Lab
]

# Asset path
# set the S3 directory manually to the latest published S3
# note: this is done to ensure prior versions of Isaac Sim still use the latest assets
[settings]
persistent.isaac.asset_root.default = "http://omniverse-content-production.s3-us-west-2.amazonaws.com/Assets/Isaac/4.5"
persistent.isaac.asset_root.cloud = "http://omniverse-content-production.s3-us-west-2.amazonaws.com/Assets/Isaac/4.5"
persistent.isaac.asset_root.nvidia = "http://omniverse-content-production.s3-us-west-2.amazonaws.com/Assets/Isaac/4.5"
