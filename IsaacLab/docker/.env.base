###
# General settings
###

# Accept the NVIDIA Omniverse EULA by default
ACCEPT_EULA=Y
# NVIDIA Isaac Sim base image
ISAACSIM_BASE_IMAGE=nvcr.io/nvidia/isaac-sim
# NVIDIA Isaac Sim version to use (e.g. 4.5.0)
ISAACSIM_VERSION=4.5.0
# Derived from the default path in the NVIDIA provided Isaac Sim container
DOCKER_ISAACSIM_ROOT_PATH=/isaac-sim
# The Isaac Lab path in the container
DOCKER_ISAACLAB_PATH=/workspace/isaaclab
# Docker user directory - by default this is the root user's home directory
DOCKER_USER_HOME=/root
# Docker image and container name suffix (default "", set by the container_interface.py script)
# Example: "-custom"
DOCKER_NAME_SUFFIX=""
