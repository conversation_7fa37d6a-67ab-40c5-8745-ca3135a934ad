﻿isaaclab.sensors.patterns
=========================

.. automodule:: isaaclab.sensors.patterns

  .. rubric:: Classes

  .. autosummary::

    PatternBaseCfg
    GridPatternCfg
    PinholeCameraPatternCfg
    BpearlPatternCfg

Pattern Base
------------

.. autoclass:: PatternBaseCfg
    :members:
    :inherited-members:
    :exclude-members: __init__

Grid Pattern
------------

.. autofunction:: isaaclab.sensors.patterns.grid_pattern

.. autoclass:: GridPatternCfg
    :members:
    :inherited-members:
    :exclude-members: __init__, func

Pinhole Camera Pattern
----------------------

.. autofunction:: isaaclab.sensors.patterns.pinhole_camera_pattern

.. autoclass:: PinholeCameraPatternCfg
    :members:
    :inherited-members:
    :exclude-members: __init__, func

RS-Bpearl <PERSON>tern
-----------------

.. autofunction:: isaaclab.sensors.patterns.bpearl_pattern

.. autoclass:: BpearlPatternCfg
    :members:
    :inherited-members:
    :exclude-members: __init__, func

LiDAR Pattern
-------------

.. autofunction:: isaaclab.sensors.patterns.lidar_pattern

.. autoclass:: LidarPatternCfg
    :members:
    :inherited-members:
    :exclude-members: __init__, func
