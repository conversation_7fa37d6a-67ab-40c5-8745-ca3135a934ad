<svg width="576" height="720" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" overflow="hidden"><defs><clipPath id="clip0"><rect x="0" y="0" width="238125" height="238125"/></clipPath><image width="32" height="32" xlink:href="data:image/png;base64,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" preserveAspectRatio="none" id="img1"></image><clipPath id="clip2"><rect x="-0.0625" y="0" width="238125" height="238125"/></clipPath><clipPath id="clip3"><rect x="0" y="0" width="247650" height="238125"/></clipPath><clipPath id="clip4"><rect x="0" y="0" width="238125" height="238125"/></clipPath><clipPath id="clip5"><rect x="0" y="0" width="247650" height="247650"/></clipPath><clipPath id="clip6"><rect x="-0.125" y="0" width="247650" height="247650"/></clipPath><clipPath id="clip7"><rect x="0" y="0" width="247650" height="238125"/></clipPath><clipPath id="clip8"><rect x="0" y="0" width="238125" height="238125"/></clipPath></defs><g><rect x="0" y="0" width="576" height="720" fill="#FFFFFF"/><path d="M38 68.6063C38 61.0917 44.0917 55 51.6063 55L539.394 55C546.908 55 553 61.0917 553 68.6063L553 651.394C553 658.908 546.908 665 539.394 665L51.6063 665C44.0917 665 38 658.908 38 651.394Z" fill="#D9F2D0" fill-rule="evenodd"/><path d="M100 162.545C100 156.169 105.169 151 111.545 151L525.454 151C531.831 151 537 156.169 537 162.545L537 581.455C537 587.831 531.831 593 525.454 593L111.545 593C105.169 593 100 587.831 100 581.455Z" fill="#FFEACD" fill-rule="evenodd"/><path d="M129 297.712C129 290.139 135.139 284 142.712 284L504.288 284C511.861 284 518 290.139 518 297.712L518 509.288C518 516.861 511.861 523 504.288 523L142.712 523C135.139 523 129 516.861 129 509.288Z" fill="#DCEAF7" fill-rule="evenodd"/><path d="M19 34C19 32.8954 19.8954 32 21 32L29 32C30.1046 32 31 32.8954 31 34L31 42C31 43.1046 30.1046 44 29 44L21 44C19.8954 44 19 43.1046 19 42Z" fill="#156082" fill-rule="evenodd"/><path d="M19 37.8334C19 36.2686 20.2686 35 21.8334 35L41.1666 35C42.7314 35 44 36.2686 44 37.8334L44 49.1666C44 50.7314 42.7314 52 41.1666 52L21.8334 52C20.2686 52 19 50.7314 19 49.1666Z" fill="#156082" fill-rule="evenodd"/><path d="M20 38.8334C20 37.2686 21.2686 36 22.8334 36L42.1666 36C43.7314 36 45 37.2686 45 38.8334L45 50.1666C45 51.7314 43.7314 53 42.1666 53L22.8334 53C21.2686 53 20 51.7314 20 50.1666Z" fill="#1D85B3" fill-rule="evenodd"/><g clip-path="url(#clip0)" transform="matrix(0.000104987 0 0 0.000104987 109 562)"><g clip-path="url(#clip2)"><use width="100%" height="100%" xlink:href="#img1" transform="matrix(7441.41 0 0 7441.41 -0.0625 0)"></use></g></g><path d="M143.958 198.375C141.479 198.375 139.468 200.446 139.468 203 139.468 205.554 141.479 207.625 143.958 207.625 146.438 207.625 148.448 205.554 148.448 203 148.448 200.446 146.438 198.375 143.958 198.375ZM141.879 190 146.079 190 147.137 194.357 147.453 194.477C148.259 194.828 149.005 195.297 149.67 195.862L149.679 195.871 153.9 194.627 156 198.373 152.824 201.537 152.834 201.591C152.902 202.051 152.938 202.521 152.938 203 152.938 203.479 152.902 203.949 152.834 204.409L152.818 204.498 155.958 207.627 153.858 211.373 149.669 210.139 148.614 210.911C148.244 211.143 147.856 211.348 147.453 211.523L147.137 211.643 146.079 216 141.879 216 140.826 211.66 140.463 211.523C140.06 211.348 139.672 211.143 139.302 210.911L138.26 210.148 134.1 211.373 132 207.627 135.105 204.534 135.082 204.409C135.014 203.949 134.979 203.479 134.979 203 134.979 202.521 135.014 202.051 135.082 201.591L135.105 201.466 132 198.373 134.1 194.627 138.26 195.852 139.302 195.089C139.672 194.857 140.06 194.652 140.463 194.477L140.826 194.34Z" fill="#FFA225" fill-rule="evenodd"/><text font-family="Aptos,Aptos_MSFontService,sans-serif" font-weight="400" font-size="14" transform="translate(50.6245 51)">isaac_lab_tutorial</text><text font-family="Aptos,Aptos_MSFontService,sans-serif" font-weight="400" font-size="14" transform="translate(84.2706 78)">scripts</text><text font-family="Aptos,Aptos_MSFontService,sans-serif" font-weight="400" font-size="14" transform="translate(84.2706 112)">source</text><text font-family="Aptos,Aptos_MSFontService,sans-serif" font-weight="400" font-size="14" transform="translate(80.8823 617)">LICENSE</text><text font-family="Aptos,Aptos_MSFontService,sans-serif" font-weight="400" font-size="14" transform="translate(80.8823 651)">README.md</text><path d="M53 643.5C53 638.253 57.0294 634 62 634 66.9706 634 71 638.253 71 643.5 71 648.747 66.9706 653 62 653 57.0294 653 53 648.747 53 643.5Z" stroke="#D86ECC" stroke-width="2" stroke-miterlimit="8" fill="none" fill-rule="evenodd"/><rect x="61" y="640" width="2" height="11" fill="#D86ECC"/><path d="M60 637.5C60 636.672 60.8954 636 62 636 63.1046 636 64 636.672 64 637.5 64 638.328 63.1046 639 62 639 60.8954 639 60 638.328 60 637.5Z" fill="#D86ECC" fill-rule="evenodd"/><path d="M68.6743 607.176C67.3835 605.885 65.2907 605.885 63.9999 607.176 62.7091 608.467 62.7091 610.56 63.9999 611.851 65.2907 613.141 67.3835 613.141 68.6743 611.851 69.9651 610.56 69.9651 608.467 68.6743 607.176ZM69.8289 606.022C71.7522 607.945 71.7522 611.063 69.8289 612.987 67.9056 614.91 64.7872 614.91 62.8639 612.987 60.9406 611.063 60.9406 607.945 62.8639 606.022 64.7872 604.098 67.9056 604.098 69.8289 606.022Z" fill="#00B050" fill-rule="evenodd"/><path d="M64.429 612.216C64.5388 612.326 64.5388 612.503 64.429 612.613L55.4004 621.642C55.2907 621.752 55.1128 621.752 55.0031 621.642L54.2086 620.847C54.0988 620.738 54.0988 620.56 54.2085 620.45L63.2372 611.421C63.3469 611.312 63.5248 611.312 63.6345 611.421Z" fill="#00B050" fill-rule="evenodd"/><path d="M55.1602 616.082C55.2699 615.973 55.4478 615.973 55.5575 616.082L57.222 617.747C57.3317 617.857 57.3317 618.035 57.222 618.144L56.4275 618.939C56.3178 619.049 56.1399 619.049 56.0302 618.939L54.3656 617.274C54.2559 617.164 54.2559 616.987 54.3656 616.877Z" fill="#00B050" fill-rule="evenodd"/><path d="M53.2951 617.947C53.4048 617.838 53.5827 617.838 53.6924 617.947L55.3569 619.612C55.4666 619.722 55.4666 619.9 55.3569 620.009L54.5624 620.804C54.4527 620.914 54.2748 620.914 54.1651 620.804L52.5005 619.139C52.3908 619.029 52.3908 618.852 52.5005 618.742Z" fill="#00B050" fill-rule="evenodd"/><path d="M52 64C52 62.8955 52.8955 62 54 62L63 62C64.1046 62 65 62.8955 65 64L65 72C65 73.1045 64.1046 74 63 74L54 74C52.8955 74 52 73.1045 52 72Z" fill="#156082" fill-rule="evenodd"/><path d="M52 67.6667C52 66.1939 53.1939 65 54.6667 65L75.3333 65C76.8061 65 78 66.1939 78 67.6667L78 78.3333C78 79.8061 76.8061 81 75.3333 81L54.6667 81C53.1939 81 52 79.8061 52 78.3333Z" fill="#156082" fill-rule="evenodd"/><path d="M53 68.8334C53 67.2686 54.2686 66 55.8334 66L76.1666 66C77.7314 66 79 67.2686 79 68.8334L79 80.1666C79 81.7314 77.7314 83 76.1666 83L55.8334 83C54.2686 83 53 81.7314 53 80.1666Z" fill="#1D85B3" fill-rule="evenodd"/><path d="M54 97C54 95.8954 54.8954 95 56 95L64 95C65.1046 95 66 95.8954 66 97L66 105C66 106.105 65.1046 107 64 107L56 107C54.8954 107 54 106.105 54 105Z" fill="#156082" fill-rule="evenodd"/><path d="M54 99.8334C54 98.2686 55.2686 97 56.8334 97L76.1666 97C77.7314 97 79 98.2686 79 99.8334L79 111.167C79 112.731 77.7314 114 76.1666 114L56.8334 114C55.2686 114 54 112.731 54 111.167Z" fill="#156082" fill-rule="evenodd"/><path d="M55 101.667C55 100.194 56.1939 99 57.6667 99L77.3333 99C78.8061 99 80 100.194 80 101.667L80 112.333C80 113.806 78.8061 115 77.3333 115L57.6667 115C56.1939 115 55 113.806 55 112.333Z" fill="#1D85B3" fill-rule="evenodd"/><path d="M80 129.167C80 127.97 80.9701 127 82.1667 127L90.8333 127C92.0299 127 93 127.97 93 129.167L93 137.833C93 139.03 92.0299 140 90.8333 140L82.1667 140C80.9701 140 80 139.03 80 137.833Z" fill="#156082" fill-rule="evenodd"/><path d="M80 132.833C80 131.269 81.2685 130 82.8334 130L103.167 130C104.731 130 106 131.269 106 132.833L106 144.167C106 145.731 104.731 147 103.167 147L82.8334 147C81.2685 147 80 145.731 80 144.167Z" fill="#156082" fill-rule="evenodd"/><path d="M81 133.833C81 132.269 82.2685 131 83.8334 131L104.167 131C105.731 131 107 132.269 107 133.833L107 145.167C107 146.731 105.731 148 104.167 148L83.8334 148C82.2685 148 81 146.731 81 145.167Z" fill="#1D85B3" fill-rule="evenodd"/><text font-family="Aptos,Aptos_MSFontService,sans-serif" font-weight="400" font-size="14" transform="translate(112.231 146)">isaac_lab_tutorial</text><path d="M107 163C107 161.895 107.895 161 109 161L117 161C118.105 161 119 161.895 119 163L119 171C119 172.105 118.105 173 117 173L109 173C107.895 173 107 172.105 107 171Z" fill="#156082" fill-rule="evenodd"/><path d="M107 165.833C107 164.269 108.269 163 109.833 163L129.167 163C130.731 163 132 164.269 132 165.833L132 177.167C132 178.731 130.731 180 129.167 180L109.833 180C108.269 180 107 178.731 107 177.167Z" fill="#156082" fill-rule="evenodd"/><path d="M108 167.833C108 166.269 109.269 165 110.833 165L130.167 165C131.731 165 133 166.269 133 167.833L133 179.167C133 180.731 131.731 182 130.167 182L110.833 182C109.269 182 108 180.731 108 179.167Z" fill="#1D85B3" fill-rule="evenodd"/><text font-family="Aptos,Aptos_MSFontService,sans-serif" font-weight="400" font-size="14" transform="translate(138.605 180)">config</text><text font-family="Aptos,Aptos_MSFontService,sans-serif" font-weight="400" font-size="14" transform="translate(138.604 546)">pyproject.toml</text><text font-family="Aptos,Aptos_MSFontService,sans-serif" font-weight="400" font-size="14" transform="translate(138.604 580)">setup.py</text><path d="M108 259.167C108 257.97 108.97 257 110.167 257L118.833 257C120.03 257 121 257.97 121 259.167L121 267.833C121 269.03 120.03 270 118.833 270L110.167 270C108.97 270 108 269.03 108 267.833Z" fill="#156082" fill-rule="evenodd"/><path d="M108 262.833C108 261.269 109.269 260 110.833 260L131.167 260C132.731 260 134 261.269 134 262.833L134 274.167C134 275.731 132.731 277 131.167 277L110.833 277C109.269 277 108 275.731 108 274.167Z" fill="#156082" fill-rule="evenodd"/><path d="M109 263.833C109 262.269 110.269 261 111.833 261L132.167 261C133.731 261 135 262.269 135 263.833L135 275.167C135 276.731 133.731 278 132.167 278L111.833 278C110.269 278 109 276.731 109 275.167Z" fill="#1D85B3" fill-rule="evenodd"/><path d="M108 227C108 225.895 108.895 225 110 225L118 225C119.105 225 120 225.895 120 227L120 235C120 236.105 119.105 237 118 237L110 237C108.895 237 108 236.105 108 235Z" fill="#156082" fill-rule="evenodd"/><path d="M108 229.833C108 228.269 109.269 227 110.833 227L130.167 227C131.731 227 133 228.269 133 229.833L133 241.167C133 242.731 131.731 244 130.167 244L110.833 244C109.269 244 108 242.731 108 241.167Z" fill="#156082" fill-rule="evenodd"/><path d="M109 231.833C109 230.269 110.269 229 111.833 229L131.167 229C132.731 229 134 230.269 134 231.833L134 243.167C134 244.731 132.731 246 131.167 246L111.833 246C110.269 246 109 244.731 109 243.167Z" fill="#1D85B3" fill-rule="evenodd"/><text font-family="Aptos,Aptos_MSFontService,sans-serif" font-weight="400" font-size="14" transform="translate(139.846 243)">docs</text><text font-family="Aptos,Aptos_MSFontService,sans-serif" font-weight="400" font-size="14" transform="translate(139.846 277)">isaac_lab_tutorial</text><text font-family="Aptos,Aptos_MSFontService,sans-serif" font-weight="400" font-size="14" transform="translate(162.086 208)">extension.toml</text><path d="M120.958 537.053C118.479 537.053 116.468 539.044 116.468 541.5 116.468 543.956 118.479 545.947 120.958 545.947 123.438 545.947 125.448 543.956 125.448 541.5 125.448 539.044 123.438 537.053 120.958 537.053ZM118.879 529 123.079 529 124.137 533.19 124.453 533.305C125.259 533.642 126.005 534.093 126.67 534.637L126.679 534.645 130.9 533.449 133 537.051 129.824 540.093 129.834 540.146C129.902 540.587 129.938 541.039 129.938 541.5 129.938 541.961 129.902 542.413 129.834 542.854L129.818 542.941 132.958 545.949 130.858 549.551 126.669 548.364 125.614 549.107C125.244 549.329 124.856 549.526 124.453 549.695L124.137 549.81 123.079 554 118.879 554 117.826 549.827 117.463 549.695C117.06 549.526 116.672 549.329 116.302 549.107L115.26 548.373 111.1 549.551 109 545.949 112.105 542.975 112.082 542.854C112.014 542.413 111.979 541.961 111.979 541.5 111.979 541.039 112.014 540.587 112.082 540.146L112.105 540.025 109 537.051 111.1 533.449 115.26 534.627 116.302 533.893C116.672 533.671 117.06 533.474 117.463 533.305L117.826 533.173Z" fill="#FFA225" fill-rule="evenodd"/><text font-family="Aptos,Aptos_MSFontService,sans-serif" font-weight="400" font-size="14" transform="translate(166.252 310)">tasks</text><text font-family="Aptos,Aptos_MSFontService,sans-serif" font-weight="400" font-size="14" transform="translate(192.944 345)">direct</text><path d="M134 293C134 291.895 134.895 291 136 291L144 291C145.105 291 146 291.895 146 293L146 302C146 303.105 145.105 304 144 304L136 304C134.895 304 134 303.105 134 302Z" fill="#156082" fill-rule="evenodd"/><path d="M134 296.833C134 295.269 135.269 294 136.833 294L156.167 294C157.731 294 159 295.269 159 296.833L159 308.167C159 309.731 157.731 311 156.167 311L136.833 311C135.269 311 134 309.731 134 308.167Z" fill="#156082" fill-rule="evenodd"/><path d="M135 297.833C135 296.269 136.269 295 137.833 295L157.167 295C158.731 295 160 296.269 160 297.833L160 309.167C160 310.731 158.731 312 157.167 312L137.833 312C136.269 312 135 310.731 135 309.167Z" fill="#1D85B3" fill-rule="evenodd"/><path d="M160 328C160 326.895 160.895 326 162 326L170 326C171.105 326 172 326.895 172 328L172 336C172 337.105 171.105 338 170 338L162 338C160.895 338 160 337.105 160 336Z" fill="#156082" fill-rule="evenodd"/><path d="M160 331.833C160 330.269 161.269 329 162.833 329L182.167 329C183.731 329 185 330.269 185 331.833L185 343.167C185 344.731 183.731 346 182.167 346L162.833 346C161.269 346 160 344.731 160 343.167Z" fill="#156082" fill-rule="evenodd"/><path d="M161 332.833C161 331.269 162.269 330 163.833 330L183.167 330C184.731 330 186 331.269 186 332.833L186 344.167C186 345.731 184.731 347 183.167 347L163.833 347C162.269 347 161 345.731 161 344.167Z" fill="#1D85B3" fill-rule="evenodd"/><path d="M31 58 31.0001 650.996" stroke="#156082" stroke-width="2" stroke-miterlimit="8" fill="none" fill-rule="evenodd"/><path d="M66 119 66.0001 593.193" stroke="#156082" stroke-width="2" stroke-miterlimit="8" fill="none" fill-rule="evenodd"/><path d="M93 154 93.0001 576.589" stroke="#156082" stroke-width="2" stroke-miterlimit="8" fill="none" fill-rule="evenodd"/><path d="M120 187 120 219.101" stroke="#156082" stroke-width="2" stroke-miterlimit="8" fill="none" fill-rule="evenodd"/><path d="M122 283 122 511.231" stroke="#156082" stroke-width="2" stroke-miterlimit="8" fill="none" fill-rule="evenodd"/><path d="M204 395.214C204 389.573 208.573 385 214.214 385L489.786 385C495.427 385 500 389.573 500 395.214L500 502.786C500 508.427 495.427 513 489.786 513L214.214 513C208.573 513 204 508.427 204 502.786Z" fill="#F2CFEE" fill-rule="evenodd"/><path d="M187 359C187 357.895 187.895 357 189 357L197 357C198.105 357 199 357.895 199 359L199 368C199 369.105 198.105 370 197 370L189 370C187.895 370 187 369.105 187 368Z" fill="#156082" fill-rule="evenodd"/><path d="M187 362.833C187 361.269 188.269 360 189.833 360L209.167 360C210.731 360 212 361.269 212 362.833L212 374.167C212 375.731 210.731 377 209.167 377L189.833 377C188.269 377 187 375.731 187 374.167Z" fill="#156082" fill-rule="evenodd"/><path d="M188 363.833C188 362.269 189.269 361 190.833 361L210.167 361C211.731 361 213 362.269 213 363.833L213 375.167C213 376.731 211.731 378 210.167 378L190.833 378C189.269 378 188 376.731 188 375.167Z" fill="#1D85B3" fill-rule="evenodd"/><text font-family="Aptos,Aptos_MSFontService,sans-serif" font-weight="400" font-size="14" transform="translate(218.751 376)">isaac_lab_tutorial</text><path d="M213 392C213 390.895 213.895 390 215 390L224 390C225.105 390 226 390.895 226 392L226 400C226 401.105 225.105 402 224 402L215 402C213.895 402 213 401.105 213 400Z" fill="#156082" fill-rule="evenodd"/><path d="M213 394.833C213 393.269 214.269 392 215.833 392L236.167 392C237.731 392 239 393.269 239 394.833L239 406.167C239 407.731 237.731 409 236.167 409L215.833 409C214.269 409 213 407.731 213 406.167Z" fill="#156082" fill-rule="evenodd"/><path d="M214 396.667C214 395.194 215.194 394 216.667 394L237.333 394C238.806 394 240 395.194 240 396.667L240 407.333C240 408.806 238.806 410 237.333 410L216.667 410C215.194 410 214 408.806 214 407.333Z" fill="#1D85B3" fill-rule="evenodd"/><text font-family="Aptos,Aptos_MSFontService,sans-serif" font-weight="400" font-size="14" transform="translate(244.752 408)">agents</text><text font-family="Aptos,Aptos_MSFontService,sans-serif" font-weight="400" font-size="14" transform="translate(242.483 439)">__init__.py</text><text font-family="Aptos,Aptos_MSFontService,sans-serif" font-weight="400" font-size="14" transform="translate(241.295 471)">isaac_lab_tutorial_env_cfg.py</text><text font-family="Aptos,Aptos_MSFontService,sans-serif" font-weight="400" font-size="14" transform="translate(242.033 504)">isaac_lab_tutorial_env.py</text><g clip-path="url(#clip3)" transform="matrix(0.000104987 0 0 0.000104987 213 420)"><g clip-path="url(#clip4)" transform="matrix(1.04 0 0 1 -0.125 -0.25)"><use width="100%" height="100%" xlink:href="#img1" transform="scale(7441.41 7441.41)"></use></g></g><g clip-path="url(#clip5)" transform="matrix(0.000104987 0 0 0.000104987 213 451)"><g clip-path="url(#clip6)"><use width="100%" height="100%" xlink:href="#img1" transform="matrix(7739.06 0 0 7739.06 -0.125 0)"></use></g></g><g clip-path="url(#clip7)" transform="matrix(0.000104987 0 0 0.000104987 213 483)"><g clip-path="url(#clip8)" transform="matrix(1.04 0 0 1 -0.125 0)"><use width="100%" height="100%" xlink:href="#img1" transform="scale(7441.41 7441.41)"></use></g></g><path d="M146 317 146 518.337" stroke="#156082" stroke-width="2" stroke-miterlimit="8" fill="none" fill-rule="evenodd"/><path d="M171 352 171 518.37" stroke="#156082" stroke-width="2" stroke-miterlimit="8" fill="none" fill-rule="evenodd"/><path d="M197 384 197 503.395" stroke="#156082" stroke-width="2" stroke-miterlimit="8" fill="none" fill-rule="evenodd"/><text fill="#00B050" font-family="Aptos,Aptos_MSFontService,sans-serif" font-weight="700" font-size="28" transform="translate(452.326 99)">Project</text><text fill="#FFA225" font-family="Aptos,Aptos_MSFontService,sans-serif" font-weight="700" font-size="28" transform="translate(401.739 193)">Extension</text><text fill="#4E95D9" font-family="Aptos,Aptos_MSFontService,sans-serif" font-weight="700" font-size="28" transform="translate(399.269 328)">Modules</text><text fill="#D86ECC" font-family="Aptos,Aptos_MSFontService,sans-serif" font-weight="700" font-size="28" transform="translate(435.682 425)">Task</text></g></svg>
